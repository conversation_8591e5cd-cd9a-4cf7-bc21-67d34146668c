#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually download the Gemma 3N E4B model from Hugging Face.

This script downloads the model files to a local directory for faster loading.
"""

import os
from pathlib import Path
from huggingface_hub import snapshot_download

def download_gemma_model():
    """Download the Gemma 3N E4B model."""
    
    # Configuration
    model_name = "google/gemma-3n-E4B-it"
    local_dir = "./models/google--gemma-3n-E4B-it"
    token = "*************************************"
    
    print(f"🚀 Starting download of {model_name}")
    print(f"📁 Local directory: {local_dir}")
    
    # Create directory if it doesn't exist
    Path(local_dir).mkdir(parents=True, exist_ok=True)
    
    try:
        # Download the model
        print("⬇️  Downloading model files...")
        snapshot_download(
            repo_id=model_name,
            local_dir=local_dir,
            token=token,
            resume_download=True,  # Resume if interrupted
            local_dir_use_symlinks=False,  # Don't use symlinks
        )
        
        print("✅ Model download completed successfully!")
        print(f"📂 Model files saved to: {os.path.abspath(local_dir)}")
        
        # List downloaded files
        print("\n📋 Downloaded files:")
        for file_path in Path(local_dir).rglob("*"):
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"  - {file_path.name}: {size_mb:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

if __name__ == "__main__":
    success = download_gemma_model()
    if success:
        print("\n🎉 Ready to use the model!")
        print("💡 Update your .env file to use the local model:")
        print("   MODEL_CACHE_DIR=./models")
        print("   MODEL_NAME=google/gemma-3n-E4B-it")
    else:
        print("\n💔 Download failed. Please check your internet connection and HF token.")
