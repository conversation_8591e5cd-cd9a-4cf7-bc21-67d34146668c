#!/usr/bin/env python3
"""
Quick script to check the download status of Gemma 3N E4B model.
"""

from pathlib import Path
import time

def check_download_status():
    """Check the current download status."""
    
    model_dir = Path("./models/google--gemma-3n-E4B-it")
    
    if not model_dir.exists():
        print("❌ Model directory not found. Download not started.")
        return
    
    print("📊 Gemma 3N E4B Download Status")
    print("=" * 40)
    
    # Expected files and their approximate sizes
    expected_files = {
        "config.json": 0.004,  # MB
        "tokenizer.json": 33.4,
        "tokenizer.model": 4.7,
        "tokenizer_config.json": 1.2,
        "model.safetensors.index.json": 0.17,
        "model-00001-of-00004.safetensors": 3080,  # ~3GB
        "model-00002-of-00004.safetensors": 4970,  # ~5GB
        "model-00003-of-00004.safetensors": 4990,  # ~5GB
        "model-00004-of-00004.safetensors": 2660,  # ~2.7GB
    }
    
    total_expected = sum(expected_files.values())
    total_downloaded = 0
    
    print("📁 File Status:")
    for filename, expected_size in expected_files.items():
        file_path = model_dir / filename
        if file_path.exists():
            actual_size = file_path.stat().st_size / (1024 * 1024)  # MB
            total_downloaded += actual_size
            
            if actual_size >= expected_size * 0.95:  # 95% threshold
                status = "✅ Complete"
            else:
                progress = (actual_size / expected_size) * 100
                status = f"⬇️  {progress:.1f}%"
            
            print(f"  {filename:<35} {actual_size:>8.1f} MB  {status}")
        else:
            print(f"  {filename:<35} {'---':>8} MB  ⏳ Waiting")
    
    print("-" * 60)
    overall_progress = (total_downloaded / total_expected) * 100
    print(f"📊 Overall Progress: {overall_progress:.1f}% ({total_downloaded:.1f}/{total_expected:.1f} MB)")
    
    if overall_progress >= 95:
        print("🎉 Download appears complete! You can now use the local model.")
        print("💡 Run: python scripts/use_local_model.py")
    elif overall_progress > 0:
        print("⏳ Download in progress...")
        estimated_remaining = total_expected - total_downloaded
        print(f"📥 Remaining: {estimated_remaining:.1f} MB")
    else:
        print("❌ No files downloaded yet.")

if __name__ == "__main__":
    check_download_status()
