#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to configure the API to use locally downloaded Gemma 3N E4B model.

This script updates the configuration to use the local model files
instead of downloading from Hugging Face each time.
"""

import os
import sys
from pathlib import Path

def check_local_model():
    """Check if the local model files exist."""
    
    model_dir = Path("./models/google--gemma-3n-E4B-it")
    
    print("🔍 Checking for local model files...")
    print(f"📁 Model directory: {model_dir.absolute()}")
    
    if not model_dir.exists():
        print("❌ Model directory not found!")
        print("💡 Run 'python download_model.py' first to download the model.")
        return False
    
    # Check for essential files
    essential_files = [
        "config.json",
        "tokenizer.json",
        "tokenizer.model",
        "tokenizer_config.json",
        "model.safetensors.index.json"
    ]
    
    missing_files = []
    for file_name in essential_files:
        file_path = model_dir / file_name
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"✅ {file_name}: {size_mb:.1f} MB")
        else:
            print(f"❌ {file_name}: Missing")
            missing_files.append(file_name)
    
    # Check for model shard files
    model_shards = list(model_dir.glob("model-*.safetensors"))
    if model_shards:
        print(f"✅ Found {len(model_shards)} model shard files:")
        total_size = 0
        for shard in sorted(model_shards):
            size_mb = shard.stat().st_size / (1024 * 1024)
            total_size += size_mb
            print(f"   - {shard.name}: {size_mb:.1f} MB")
        print(f"📊 Total model size: {total_size:.1f} MB ({total_size/1024:.1f} GB)")
    else:
        print("❌ No model shard files found")
        missing_files.append("model-*.safetensors")
    
    if missing_files:
        print(f"\n⚠️  Missing files: {', '.join(missing_files)}")
        print("💡 The model download may still be in progress.")
        return False
    
    print("\n✅ All essential model files found!")
    return True

def update_env_config():
    """Update .env file to use local model."""
    
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env file not found!")
        print("💡 Copy .env.example to .env first.")
        return False
    
    # Read current .env content
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Update relevant lines
    updated_lines = []
    updated_cache_dir = False
    
    for line in lines:
        if line.startswith("MODEL_CACHE_DIR="):
            updated_lines.append("MODEL_CACHE_DIR=./models\n")
            updated_cache_dir = True
            print("✅ Updated MODEL_CACHE_DIR to ./models")
        elif line.startswith("MODEL_NAME="):
            updated_lines.append("MODEL_NAME=google/gemma-3n-E4B-it\n")
            print("✅ Updated MODEL_NAME to google/gemma-3n-E4B-it")
        else:
            updated_lines.append(line)
    
    # Add MODEL_CACHE_DIR if it wasn't found
    if not updated_cache_dir:
        updated_lines.append("MODEL_CACHE_DIR=./models\n")
        print("✅ Added MODEL_CACHE_DIR=./models")
    
    # Write updated content
    with open(env_file, 'w') as f:
        f.writelines(updated_lines)
    
    print("✅ .env file updated successfully!")
    return True

def test_model_loading():
    """Test if the model can be loaded successfully."""
    
    print("\n🧪 Testing model loading...")
    
    try:
        # Add src to path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        from gemma_3n.core.config import get_settings
        from transformers import AutoTokenizer
        
        settings = get_settings()
        
        print(f"📋 Model name: {settings.model_name}")
        print(f"📁 Cache directory: {settings.model_cache_dir}")
        
        # Try to load tokenizer
        print("🔤 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            settings.model_name,
            cache_dir=settings.model_cache_dir,
            token=settings.hf_token,
            local_files_only=True  # Only use local files
        )
        
        print("✅ Tokenizer loaded successfully!")
        print(f"📊 Vocabulary size: {tokenizer.vocab_size}")
        
        # Test tokenization
        test_text = "Hello, how are you?"
        tokens = tokenizer(test_text, return_tensors="pt")
        print(f"🧪 Test tokenization: '{test_text}' -> {tokens.input_ids.shape[1]} tokens")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading test failed: {e}")
        return False

def main():
    """Main function."""
    
    print("🚀 Gemma 3N E4B Local Model Setup")
    print("=" * 50)
    
    # Check if model files exist
    if not check_local_model():
        print("\n💡 Next steps:")
        print("1. Run 'python download_model.py' to download the model")
        print("2. Wait for download to complete")
        print("3. Run this script again")
        return False
    
    # Update .env configuration
    print("\n⚙️  Updating configuration...")
    if not update_env_config():
        return False
    
    # Test model loading
    if not test_model_loading():
        print("\n⚠️  Model loading test failed.")
        print("💡 The model files may still be downloading or corrupted.")
        return False
    
    print("\n🎉 Local model setup complete!")
    print("\n🚀 Next steps:")
    print("1. Start the API server: python main.py")
    print("2. The model will load from local files (much faster!)")
    print("3. Test the API at http://localhost:8000/docs")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
