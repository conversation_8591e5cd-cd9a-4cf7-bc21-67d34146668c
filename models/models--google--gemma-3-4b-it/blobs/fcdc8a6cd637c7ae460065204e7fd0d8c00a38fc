{"architectures": ["Gemma3ForConditionalGeneration"], "boi_token_index": 255999, "eoi_token_index": 256000, "eos_token_id": [1, 106], "image_token_index": 262144, "initializer_range": 0.02, "mm_tokens_per_image": 256, "model_type": "gemma3", "text_config": {"hidden_size": 2560, "intermediate_size": 10240, "model_type": "gemma3_text", "num_hidden_layers": 34, "rope_scaling": {"factor": 8.0, "rope_type": "linear"}, "sliding_window": 1024}, "torch_dtype": "bfloat16", "transformers_version": "4.50.0.dev0", "vision_config": {"hidden_size": 1152, "image_size": 896, "intermediate_size": 4304, "model_type": "siglip_vision_model", "num_attention_heads": 16, "num_hidden_layers": 27, "patch_size": 14, "vision_use_head": false}}