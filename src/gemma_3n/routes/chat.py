"""
Chat endpoints for educational assistance.

This module provides the main chat functionality for the educational
assistant, handling multimodal inputs and generating educational responses.
"""

from uuid import uuid4
from typing import List

from fastapi import APIRouter, Depends, Request, HTTPException, status
from loguru import logger

from ..core.config import Settings, get_settings
from ..core.models import ChatRequest, ChatResponse, ProcessingStatus
from ..core.exceptions import (
    EducationalAssistantError,
    InvalidInputError,
    NonEducationalContentError,
    ModelGenerationError
)
from ..services.chat_service import ChatService
from ..services.content_filter import ContentFilter
from ..services.input_processor import InputProcessor


router = APIRouter()


@router.post("/", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    app_request: Request,
    settings: Settings = Depends(get_settings)
) -> ChatResponse:
    """
    Main chat endpoint for educational assistance.

    Processes multimodal inputs (text, images, audio) and generates
    educational responses using the Gemma 3 model.
    """
    session_id = None
    try:
        # Validate request
        if not request.inputs:
            raise InvalidInputError("No inputs provided")

        # Get and validate model manager
        if not hasattr(app_request.app.state, 'model_manager'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Model manager not initialized"
            )

        model_manager = app_request.app.state.model_manager

        if not model_manager.is_ready():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Model is not ready for inference"
            )

        # Initialize services with error handling
        try:
            input_processor = InputProcessor(settings)
            content_filter = ContentFilter(settings)
            chat_service = ChatService(model_manager, settings)
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Service initialization failed"
            )

        # Generate session ID if not provided
        session_id = request.session_id or uuid4()

        # Validate session ID
        if not isinstance(session_id, UUID):
            try:
                session_id = UUID(str(session_id))
            except ValueError:
                session_id = uuid4()

        # Process inputs with detailed error tracking
        logger.info(f"Processing {len(request.inputs)} inputs for session {session_id}")
        processing_statuses: List[ProcessingStatus] = []
        processed_content = []

        for i, input_item in enumerate(request.inputs):
            try:
                # Validate input item
                if not input_item.content:
                    raise InvalidInputError(f"Empty content in input {i+1}")

                # Process the input based on type
                logger.debug(f"Processing input {i+1}/{len(request.inputs)}: {input_item.type}")
                processed = await input_processor.process_input(input_item)

                if processed and processed.strip():
                    processing_statuses.append(ProcessingStatus(
                        input_type=input_item.type,
                        status="success",
                        processed_content=processed[:500] + "..." if len(processed) > 500 else processed
                    ))
                    processed_content.append(processed)
                else:
                    processing_statuses.append(ProcessingStatus(
                        input_type=input_item.type,
                        status="warning",
                        message="Input processed but produced no meaningful content"
                    ))

            except (InvalidInputError, UnsupportedFileTypeError, FileTooLargeError) as e:
                logger.warning(f"Input validation error for input {i+1}: {e}")
                processing_statuses.append(ProcessingStatus(
                    input_type=input_item.type,
                    status="error",
                    message=str(e)
                ))
            except Exception as e:
                logger.error(f"Failed to process input {i+1} ({input_item.type}): {e}")
                processing_statuses.append(ProcessingStatus(
                    input_type=input_item.type,
                    status="error",
                    message=f"Processing failed: {str(e)[:100]}"
                ))

        # Check if we have any successfully processed content
        if not processed_content:
            raise InvalidInputError(
                "No inputs could be processed successfully. Please check your input format and try again."
            )

        # Combine all processed content with validation
        combined_content = " ".join(filter(None, processed_content))

        if not combined_content.strip():
            raise InvalidInputError("All processed inputs resulted in empty content")

        # Validate content length
        if len(combined_content) > settings.max_input_length:
            logger.warning(f"Combined content too long ({len(combined_content)} chars), truncating")
            combined_content = combined_content[:settings.max_input_length] + "... [truncated]"

        # Filter content for educational appropriateness
        try:
            if not content_filter.is_educational_content(combined_content):
                raise NonEducationalContentError(
                    "I can only help with educational and academic topics. "
                    "Please ask questions related to learning, homework, or academic subjects."
                )
        except Exception as e:
            logger.error(f"Content filtering error: {e}")
            # If content filtering fails, allow the request but log the issue
            logger.warning("Content filtering failed, proceeding with request")

        # Generate response using chat service with error handling
        try:
            response_text, reasoning_steps, follow_ups = await chat_service.generate_response(
                session_id=session_id,
                content=combined_content,
                educational_level=request.educational_level,
                subject=request.subject,
                max_tokens=request.max_tokens or settings.default_max_tokens,
                temperature=request.temperature or settings.default_temperature,
                include_reasoning=request.include_reasoning or False
            )
        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            raise ModelGenerationError(f"Failed to generate educational response: {str(e)}")

        # Validate response
        if not response_text or not response_text.strip():
            logger.warning("Generated empty response")
            response_text = "I apologize, but I couldn't generate a proper response. Please try rephrasing your question."
            reasoning_steps = None
            follow_ups = []

        # Create educational context with error handling
        try:
            educational_context = {
                "level": request.educational_level.value,
                "subject": request.subject.value,
                "input_types": [inp.type.value for inp in request.inputs],
                "processing_summary": {
                    "total_inputs": len(request.inputs),
                    "successful": len([s for s in processing_statuses if s.status == "success"]),
                    "failed": len([s for s in processing_statuses if s.status == "error"]),
                    "warnings": len([s for s in processing_statuses if s.status == "warning"])
                },
                "content_length": len(combined_content),
                "response_length": len(response_text)
            }
        except Exception as e:
            logger.warning(f"Failed to create educational context: {e}")
            educational_context = {"error": "Failed to create context"}

        # Create and return response
        return ChatResponse(
            session_id=session_id,
            response=response_text,
            processing_status=processing_statuses,
            educational_context=educational_context,
            reasoning_steps=reasoning_steps if request.include_reasoning else None,
            suggested_follow_ups=follow_ups or []
        )

    except EducationalAssistantError:
        # Re-raise our custom exceptions
        raise
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {e}", exc_info=True)

        # Return a safe error response
        safe_session_id = session_id or uuid4()
        return ChatResponse(
            session_id=safe_session_id,
            response="I apologize, but I encountered an unexpected error. Please try again with a different question.",
            processing_status=[ProcessingStatus(
                input_type="text",
                status="error",
                message="Unexpected server error"
            )],
            educational_context={"error": "Unexpected error occurred"},
            suggested_follow_ups=["Please try rephrasing your question", "Check if your input format is correct"]
        )


@router.post("/stream")
async def chat_stream(
    request: ChatRequest,
    app_request: Request,
    settings: Settings = Depends(get_settings)
):
    """
    Streaming chat endpoint for real-time responses.
    
    Note: This is a placeholder for future streaming implementation.
    """
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Streaming chat is not yet implemented"
    )
