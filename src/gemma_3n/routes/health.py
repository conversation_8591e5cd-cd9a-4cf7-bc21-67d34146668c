"""
Health check endpoints.

This module provides health check and status endpoints for monitoring
the application and its dependencies.
"""

from fastapi import API<PERSON>out<PERSON>, Depends, Request
from loguru import logger

from ..core.config import Settings, get_settings
from ..core.models import HealthCheck


router = APIRouter()


@router.get("/", response_model=HealthCheck)
async def health_check(
    request: Request,
    settings: Settings = Depends(get_settings)
) -> HealthCheck:
    """
    Health check endpoint.
    
    Returns the current health status of the application and its components.
    """
    try:
        model_status = {}
        
        # Check model manager status
        if hasattr(request.app.state, 'model_manager'):
            model_manager = request.app.state.model_manager
            model_status = {
                "gemma3_model": "loaded" if model_manager.is_model_loaded() else "not_loaded",
                "processor": "loaded" if model_manager.is_processor_loaded() else "not_loaded"
            }
        else:
            model_status = {
                "gemma3_model": "not_initialized",
                "processor": "not_initialized"
            }
        
        return HealthCheck(
            status="healthy",
            version=settings.app_version,
            model_status=model_status
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheck(
            status="unhealthy",
            version=settings.app_version,
            model_status={"error": str(e)}
        )


@router.get("/ready", response_model=HealthCheck)
async def readiness_check(
    request: Request,
    settings: Settings = Depends(get_settings)
) -> HealthCheck:
    """
    Readiness check endpoint.
    
    Returns whether the application is ready to serve requests.
    """
    try:
        # Check if model manager is initialized and ready
        if not hasattr(request.app.state, 'model_manager'):
            return HealthCheck(
                status="not_ready",
                version=settings.app_version,
                model_status={"error": "Model manager not initialized"}
            )
        
        model_manager = request.app.state.model_manager
        if not model_manager.is_ready():
            return HealthCheck(
                status="not_ready",
                version=settings.app_version,
                model_status={"error": "Model not ready"}
            )
        
        return HealthCheck(
            status="ready",
            version=settings.app_version,
            model_status={
                "gemma3_model": "ready",
                "processor": "ready"
            }
        )
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return HealthCheck(
            status="not_ready",
            version=settings.app_version,
            model_status={"error": str(e)}
        )
