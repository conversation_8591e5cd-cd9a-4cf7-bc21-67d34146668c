"""
Configuration settings for the educational chat API.

This module handles all configuration settings using Pydantic Settings
with environment variable support.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # API Configuration
    app_name: str = Field("Gemma 3N Educational Chat API", env="APP_NAME")
    app_version: str = Field("0.1.0", env="APP_VERSION")
    debug: bool = Field(False, env="DEBUG")
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")
    
    # Security
    secret_key: str = Field("your-secret-key-change-in-production", env="SECRET_KEY")
    allowed_origins: List[str] = Field(["*"], env="ALLOWED_ORIGINS")
    
    # Model Configuration
    model_name: str = Field("google/gemma-3n-E4B-it", env="MODEL_NAME")
    model_cache_dir: str = Field("./models", env="MODEL_CACHE_DIR")
    device: str = Field("auto", env="DEVICE")  # auto, cpu, cuda
    max_model_memory: Optional[str] = Field(None, env="MAX_MODEL_MEMORY")
    hf_token: Optional[str] = Field(None, env="HF_TOKEN")

    # Quantization Configuration
    use_quantization: bool = Field(False, env="USE_QUANTIZATION")
    quantization_type: str = Field("4bit", env="QUANTIZATION_TYPE")  # 4bit, 8bit
    load_in_4bit: bool = Field(False, env="LOAD_IN_4BIT")
    load_in_8bit: bool = Field(False, env="LOAD_IN_8BIT")
    bnb_4bit_compute_dtype: str = Field("float16", env="BNB_4BIT_COMPUTE_DTYPE")
    bnb_4bit_quant_type: str = Field("nf4", env="BNB_4BIT_QUANT_TYPE")
    bnb_4bit_use_double_quant: bool = Field(True, env="BNB_4BIT_USE_DOUBLE_QUANT")
    llm_int8_enable_fp32_cpu_offload: bool = Field(False, env="LLM_INT8_ENABLE_FP32_CPU_OFFLOAD")
    offload_buffers: bool = Field(False, env="OFFLOAD_BUFFERS")
    
    # Generation Parameters
    default_max_tokens: int = Field(1024, env="DEFAULT_MAX_TOKENS")
    max_max_tokens: int = Field(2048, env="MAX_MAX_TOKENS")
    default_temperature: float = Field(0.7, env="DEFAULT_TEMPERATURE")
    default_top_p: float = Field(0.9, env="DEFAULT_TOP_P")
    default_top_k: int = Field(50, env="DEFAULT_TOP_K")
    default_repetition_penalty: float = Field(1.1, env="DEFAULT_REPETITION_PENALTY")
    
    # Input Processing
    max_input_length: int = Field(4096, env="MAX_INPUT_LENGTH")
    max_file_size_mb: int = Field(10, env="MAX_FILE_SIZE_MB")
    supported_image_types: List[str] = Field(
        ["image/jpeg", "image/png", "image/gif", "image/webp"],
        env="SUPPORTED_IMAGE_TYPES"
    )
    supported_audio_types: List[str] = Field(
        ["audio/wav", "audio/mp3", "audio/m4a", "audio/ogg"],
        env="SUPPORTED_AUDIO_TYPES"
    )
    
    # OCR Configuration
    ocr_engine: str = Field("easyocr", env="OCR_ENGINE")  # easyocr, tesseract
    ocr_languages: List[str] = Field(["en"], env="OCR_LANGUAGES")
    
    # Audio Processing
    audio_sample_rate: int = Field(16000, env="AUDIO_SAMPLE_RATE")
    audio_chunk_length: int = Field(30, env="AUDIO_CHUNK_LENGTH")  # seconds
    
    # Database Configuration
    database_url: str = Field("sqlite:///./conversations.db", env="DATABASE_URL")
    redis_url: Optional[str] = Field(None, env="REDIS_URL")
    
    # Session Management
    session_timeout_minutes: int = Field(60, env="SESSION_TIMEOUT_MINUTES")
    max_conversation_length: int = Field(50, env="MAX_CONVERSATION_LENGTH")
    
    # Educational Assistant Configuration
    educational_system_prompt: str = Field(
        """You are an educational assistant designed to help students learn and understand academic concepts. 
        Your role is to:
        1. Provide clear, age-appropriate explanations
        2. Encourage critical thinking and learning
        3. Guide students to discover answers rather than just providing them
        4. Only respond to educational and academic queries
        5. Refuse to help with non-educational content
        6. Maintain a supportive and encouraging tone
        
        Always focus on educational value and learning outcomes.""",
        env="EDUCATIONAL_SYSTEM_PROMPT"
    )
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # Rate Limiting
    rate_limit_requests: int = Field(100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(3600, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Monitoring
    enable_metrics: bool = Field(True, env="ENABLE_METRICS")
    metrics_port: int = Field(9090, env="METRICS_PORT")
    
    @validator('model_name')
    def validate_model_name(cls, v):
        """Validate model name format."""
        if not v or "/" not in v:
            raise ValueError("Model name must be in format 'organization/model-name'")
        return v

    @validator('max_file_size_mb')
    def validate_file_size(cls, v):
        """Validate file size limits."""
        if v <= 0 or v > 100:
            raise ValueError("File size must be between 1 and 100 MB")
        return v

    @validator('max_input_length')
    def validate_input_length(cls, v):
        """Validate input length."""
        if v <= 0 or v > 32768:
            raise ValueError("Input length must be between 1 and 32768 characters")
        return v

    @validator('default_temperature')
    def validate_temperature(cls, v):
        """Validate temperature range."""
        if not 0.0 <= v <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        return v

    @validator('port')
    def validate_port(cls, v):
        """Validate port number."""
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def validate_settings(self) -> List[str]:
        """Validate all settings and return list of warnings."""
        warnings = []

        # Check model cache directory
        if not os.path.exists(self.model_cache_dir):
            try:
                os.makedirs(self.model_cache_dir, exist_ok=True)
            except Exception:
                warnings.append(f"Cannot create model cache directory: {self.model_cache_dir}")

        # Check device compatibility
        if self.device == "cuda":
            try:
                import torch
                if not torch.cuda.is_available():
                    warnings.append("CUDA device specified but not available, will fallback to CPU")
            except ImportError:
                warnings.append("PyTorch not available, cannot check CUDA compatibility")

        # Check memory settings
        if self.max_model_memory:
            try:
                memory_str = self.max_model_memory.upper()
                if not (memory_str.endswith("GB") or memory_str.endswith("MB")):
                    warnings.append("Invalid memory format, should be like '8GB' or '512MB'")
            except Exception:
                warnings.append("Invalid memory format")

        # Check rate limiting
        if self.rate_limit_requests <= 0:
            warnings.append("Rate limiting disabled (requests <= 0)")

        return warnings


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
