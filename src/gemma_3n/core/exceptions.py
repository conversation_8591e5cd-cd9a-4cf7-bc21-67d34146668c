"""
Custom exceptions for the educational chat API.

This module defines all custom exceptions used throughout the application
for better error handling and user feedback.
"""

from typing import Optional, Dict, Any


class EducationalAssistantError(Exception):
    """Base exception for all educational assistant errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class InvalidInputError(EducationalAssistantError):
    """Raised when input validation fails."""
    pass


class ModelLoadError(EducationalAssistantError):
    """Raised when model loading fails."""
    pass


class ProcessingError(EducationalAssistantError):
    """Raised when input processing fails."""
    pass


class NonEducationalContentError(EducationalAssistantError):
    """Raised when non-educational content is detected."""
    pass


class SessionNotFoundError(EducationalAssistantError):
    """Raised when a session is not found."""
    pass


class RateLimitExceededError(EducationalAssistantError):
    """Raised when rate limit is exceeded."""
    pass


class FileTooLargeError(EducationalAssistantError):
    """Raised when uploaded file is too large."""
    pass


class UnsupportedFileTypeError(EducationalAssistantError):
    """Raised when file type is not supported."""
    pass


class OCRError(ProcessingError):
    """Raised when OCR processing fails."""
    pass


class AudioProcessingError(ProcessingError):
    """Raised when audio processing fails."""
    pass


class ModelGenerationError(EducationalAssistantError):
    """Raised when model generation fails."""
    pass
