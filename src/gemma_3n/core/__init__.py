"""
Core module for Gemma 3N educational chat API.

This module contains the core functionality including models, configuration,
and base classes for the educational assistant.
"""

from .models import (
    ChatRequest,
    ChatResponse,
    ConversationHistory,
    MessageRole,
    MessageType,
    InputType,
    EducationalLevel,
    Subject,
)
from .config import Settings, get_settings
from .exceptions import (
    EducationalAssistantError,
    InvalidInputError,
    ModelLoadError,
    ProcessingError,
)

__all__ = [
    "ChatRequest",
    "ChatResponse", 
    "ConversationHistory",
    "MessageRole",
    "MessageType",
    "InputType",
    "EducationalLevel",
    "Subject",
    "Settings",
    "get_settings",
    "EducationalAssistantError",
    "InvalidInputError",
    "ModelLoadError",
    "ProcessingError",
]
