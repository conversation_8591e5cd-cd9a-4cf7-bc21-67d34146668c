"""
Pydantic models for the educational chat API.

This module defines all the request/response models and data structures
used throughout the application.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Union, Dict, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator


class MessageRole(str, Enum):
    """Role of the message sender."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageType(str, Enum):
    """Type of message content."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    MULTIMODAL = "multimodal"


class InputType(str, Enum):
    """Type of input data."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"


class EducationalLevel(str, Enum):
    """Educational level for age-appropriate responses."""
    ELEMENTARY = "elementary"  # K-5
    MIDDLE_SCHOOL = "middle_school"  # 6-8
    HIGH_SCHOOL = "high_school"  # 9-12
    COLLEGE = "college"  # Undergraduate
    GRADUATE = "graduate"  # Graduate level
    ADULT_LEARNING = "adult_learning"  # Adult education


class Subject(str, Enum):
    """Academic subjects for educational assistance."""
    MATHEMATICS = "mathematics"
    SCIENCE = "science"
    ENGLISH = "english"
    HISTORY = "history"
    GEOGRAPHY = "geography"
    PHYSICS = "physics"
    CHEMISTRY = "chemistry"
    BIOLOGY = "biology"
    COMPUTER_SCIENCE = "computer_science"
    FOREIGN_LANGUAGE = "foreign_language"
    ART = "art"
    MUSIC = "music"
    GENERAL = "general"


class InputContent(BaseModel):
    """Content of an input (text, image, or audio)."""
    type: InputType
    content: str = Field(..., description="Base64 encoded content for files, plain text for text")
    filename: Optional[str] = Field(None, description="Original filename for uploaded files")
    mime_type: Optional[str] = Field(None, description="MIME type of the content")


class Message(BaseModel):
    """A single message in a conversation."""
    id: UUID = Field(default_factory=uuid4)
    role: MessageRole
    content: str
    message_type: MessageType = MessageType.TEXT
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    session_id: Optional[UUID] = Field(None, description="Session ID for conversation continuity")
    inputs: List[InputContent] = Field(..., min_items=1, description="List of inputs (text, images, audio)")
    educational_level: Optional[EducationalLevel] = Field(
        EducationalLevel.HIGH_SCHOOL, 
        description="Educational level for age-appropriate responses"
    )
    subject: Optional[Subject] = Field(
        Subject.GENERAL, 
        description="Academic subject for context"
    )
    max_tokens: Optional[int] = Field(1024, ge=1, le=2048, description="Maximum tokens in response")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Response creativity")
    include_reasoning: Optional[bool] = Field(
        False, 
        description="Include step-by-step reasoning in response"
    )
    
    @validator('inputs')
    def validate_inputs(cls, v):
        """Validate that at least one input is provided and types are valid."""
        if not v:
            raise ValueError("At least one input must be provided")
        
        # Check for valid combinations
        text_inputs = [inp for inp in v if inp.type == InputType.TEXT]
        if not text_inputs:
            raise ValueError("At least one text input is required")
            
        return v


class ProcessingStatus(BaseModel):
    """Status of input processing."""
    input_type: InputType
    status: str = Field(..., description="Processing status (success, error, processing)")
    message: Optional[str] = Field(None, description="Status message or error details")
    processed_content: Optional[str] = Field(None, description="Processed content (e.g., OCR text, transcription)")


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    session_id: UUID
    message_id: UUID = Field(default_factory=uuid4)
    response: str = Field(..., description="The assistant's response")
    processing_status: List[ProcessingStatus] = Field(
        default_factory=list,
        description="Status of processing for each input"
    )
    educational_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Educational context and metadata"
    )
    reasoning_steps: Optional[List[str]] = Field(
        None,
        description="Step-by-step reasoning if requested"
    )
    suggested_follow_ups: List[str] = Field(
        default_factory=list,
        description="Suggested follow-up questions"
    )
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class ConversationHistory(BaseModel):
    """Complete conversation history for a session."""
    session_id: UUID
    messages: List[Message] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    educational_level: EducationalLevel = EducationalLevel.HIGH_SCHOOL
    subject: Subject = Subject.GENERAL
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class HealthCheck(BaseModel):
    """Health check response model."""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = "0.1.0"
    model_status: Dict[str, str] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[UUID] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
