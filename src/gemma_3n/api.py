"""
FastAPI application for the educational chat API.

This module sets up the main FastAPI application with all routes,
middleware, and error handling.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from .core.config import get_settings
from .core.exceptions import EducationalAssistantError
from .core.models import ErrorResponse
from .routes import chat, health, sessions
from .services.model_manager import ModelManager


settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Gemma 3N Educational Chat API")
    
    # Initialize model manager
    try:
        model_manager = ModelManager()
        await model_manager.initialize()
        app.state.model_manager = model_manager
        logger.info("Model manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize model manager: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Gemma 3N Educational Chat API")
    if hasattr(app.state, 'model_manager'):
        await app.state.model_manager.cleanup()


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Educational Chat API with Gemma 3 - Multimodal AI Assistant for Learning",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", settings.host]
    )


# Exception handlers
@app.exception_handler(EducationalAssistantError)
async def educational_assistant_error_handler(
    request: Request, 
    exc: EducationalAssistantError
) -> JSONResponse:
    """Handle custom educational assistant errors."""
    logger.error(f"Educational assistant error: {exc.message}")
    
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=ErrorResponse(
            error=exc.error_code,
            message=exc.message,
        ).dict()
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(
    request: Request, 
    exc: HTTPException
) -> JSONResponse:
    """Handle HTTP exceptions."""
    logger.error(f"HTTP error {exc.status_code}: {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=f"HTTP_{exc.status_code}",
            message=exc.detail,
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """Handle general exceptions."""
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="INTERNAL_SERVER_ERROR",
            message="An unexpected error occurred. Please try again later.",
        ).dict()
    )


# Include routers
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(chat.router, prefix="/chat", tags=["Chat"])
app.include_router(sessions.router, prefix="/sessions", tags=["Sessions"])


@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root endpoint with API information."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": "Educational Chat API with Gemma 3 - Multimodal AI Assistant for Learning",
        "docs_url": "/docs" if settings.debug else None,
        "health_url": "/health",
        "chat_url": "/chat",
    }


# Configure logging
def setup_logging():
    """Setup application logging."""
    # Remove default loguru handler
    logger.remove()
    
    # Add custom handler
    logger.add(
        sink=lambda message: print(message, end=""),
        format=settings.log_format,
        level=settings.log_level,
        colorize=True
    )
    
    # Configure uvicorn logging
    logging.getLogger("uvicorn.access").handlers = []
    logging.getLogger("uvicorn.error").handlers = []


# Initialize logging
setup_logging()
