"""
Model manager for loading and managing the Gemma 3 model.

This module handles the loading, initialization, and management of the
Gemma 3 model and its associated processor for educational assistance.
"""

import asyncio
import os
from typing import Optional, Dict, Any, List
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoProcessor, BitsAndBytesConfig
from loguru import logger

from ..core.config import get_settings, Settings
from ..core.exceptions import ModelLoadError


class ModelManager:
    """Manages the Gemma 3 model and processor."""
    
    def __init__(self):
        self.settings = get_settings()
        self.model: Optional[AutoModelForCausalLM] = None
        self.tokenizer: Optional[AutoTokenizer] = None
        self.processor: Optional[AutoProcessor] = None
        self.device = self._determine_device()
        self._model_loaded = False
        self._processor_loaded = False
        
    def _determine_device(self) -> str:
        """Determine the best device for model inference."""
        if self.settings.device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"
            else:
                return "cpu"
        return self.settings.device
    
    async def initialize(self) -> None:
        """Initialize the model and processor asynchronously."""
        try:
            logger.info("Initializing Gemma 3 model manager...")
            
            # Create cache directory if it doesn't exist
            os.makedirs(self.settings.model_cache_dir, exist_ok=True)
            
            # Load model and tokenizer in a thread pool to avoid blocking
            await asyncio.get_event_loop().run_in_executor(
                None, self._load_model_sync
            )
            
            logger.info("Model manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize model manager: {e}")
            raise ModelLoadError(f"Model initialization failed: {str(e)}")
    
    def _load_model_sync(self) -> None:
        """Load model and tokenizer synchronously with robust error handling."""
        try:
            logger.info(f"Loading model {self.settings.model_name} on device {self.device}")

            # Clear GPU memory before loading to prevent crashes
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("Cleared GPU cache before model loading")

            # Validate model name format
            if not self.settings.model_name or "/" not in self.settings.model_name:
                raise ModelLoadError(f"Invalid model name format: {self.settings.model_name}")

            # Check available memory before loading
            self._check_system_resources()

            # Load tokenizer with retry logic
            self.tokenizer = self._load_tokenizer_with_retry()

            # Configure model loading parameters with ultra-conservative settings
            model_kwargs = self._get_model_kwargs()

            # Load model with progress tracking and memory monitoring
            logger.info("Loading model weights with aggressive memory management...")

            # Monitor GPU memory during loading
            if torch.cuda.is_available():
                gpu_memory_before = torch.cuda.memory_allocated() / 1024**3
                logger.info(f"GPU memory before loading: {gpu_memory_before:.2f}GB")

            self.model = AutoModelForCausalLM.from_pretrained(
                self.settings.model_name,
                **model_kwargs
            )

            # Monitor GPU memory after loading
            if torch.cuda.is_available():
                gpu_memory_after = torch.cuda.memory_allocated() / 1024**3
                gpu_memory_reserved = torch.cuda.memory_reserved() / 1024**3
                logger.info(f"GPU memory after loading: {gpu_memory_after:.2f}GB allocated, {gpu_memory_reserved:.2f}GB reserved")

            # Move to device if not using device_map and not quantized
            if (self.device != "cuda" and self.model is not None and
                not self.settings.use_quantization):
                logger.info(f"Moving model to device: {self.device}")
                self.model = self.model.to(self.device)

            # Validate model is working
            self._validate_model()

            # Try to load processor for multimodal capabilities
            self._load_processor()

            self._model_loaded = True
            logger.info("Model loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            self._cleanup_partial_load()
            raise ModelLoadError(f"Model loading failed: {str(e)}")

    def _check_system_resources(self) -> None:
        """Check if system has sufficient resources for model loading."""
        try:
            import psutil

            # Check available RAM
            available_ram_gb = psutil.virtual_memory().available / (1024**3)
            total_ram_gb = psutil.virtual_memory().total / (1024**3)
            logger.info(f"System RAM: {available_ram_gb:.1f}GB available / {total_ram_gb:.1f}GB total")

            # Check GPU memory if using CUDA
            if torch.cuda.is_available():
                gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()) / (1024**3)

                logger.info(f"GPU memory: {gpu_memory_free:.1f}GB free / {gpu_memory_gb:.1f}GB total")

                # For 6GB RTX 3060 laptop, warn if less than 4GB free
                if gpu_memory_free < 4.0:
                    logger.warning(
                        f"Limited GPU memory: {gpu_memory_free:.1f}GB free. "
                        "Using aggressive quantization and CPU offloading to prevent crashes."
                    )

                # Clear any existing GPU memory
                torch.cuda.empty_cache()
                logger.info("Cleared GPU cache for maximum available memory")

        except ImportError:
            logger.warning("psutil not available, skipping resource check")
        except Exception as e:
            logger.warning(f"Resource check failed: {e}")

    def _load_tokenizer_with_retry(self, max_retries: int = 3) -> AutoTokenizer:
        """Load tokenizer with retry logic."""
        for attempt in range(max_retries):
            try:
                logger.info(f"Loading tokenizer (attempt {attempt + 1}/{max_retries})")
                return AutoTokenizer.from_pretrained(
                    self.settings.model_name,
                    cache_dir=self.settings.model_cache_dir,
                    trust_remote_code=True,
                    use_fast=True,  # Use fast tokenizer when available
                    token=self.settings.hf_token  # Use HF token for authentication
                )
            except Exception as e:
                if attempt == max_retries - 1:
                    raise ModelLoadError(f"Failed to load tokenizer after {max_retries} attempts: {e}")
                logger.warning(f"Tokenizer loading attempt {attempt + 1} failed: {e}")

        raise ModelLoadError("Unexpected error in tokenizer loading")

    def _get_model_kwargs(self) -> dict:
        """Get model loading configuration based on device and settings."""
        model_kwargs = {
            "cache_dir": self.settings.model_cache_dir,
            "trust_remote_code": True,
            "torch_dtype": torch.float16 if self.device != "cpu" else torch.float32,
            "low_cpu_mem_usage": True,  # Reduce CPU memory usage during loading
            "token": self.settings.hf_token,  # Use HF token for authentication
        }

        # Add quantization configuration if enabled
        if self.settings.use_quantization:
            quantization_config = self._get_quantization_config()
            if quantization_config:
                model_kwargs["quantization_config"] = quantization_config
                logger.info(f"Using {self.settings.quantization_type} quantization for memory efficiency")

        # Configure device mapping with aggressive memory management for laptop
        if self.device == "cuda" and torch.cuda.is_available():
            # Very conservative GPU memory allocation for 6GB RTX 3060 laptop
            gpu_memory_gb = 2  # Use only 2GB to prevent crashes
            cpu_memory_gb = 16  # Offload rest to CPU

            model_kwargs["device_map"] = "auto"
            model_kwargs["max_memory"] = {
                0: f"{gpu_memory_gb}GB",  # GPU 0
                "cpu": f"{cpu_memory_gb}GB"  # CPU fallback
            }

            # Enable all offloading options to prevent crashes
            if self.settings.use_quantization:
                model_kwargs["offload_buffers"] = True
                model_kwargs["offload_state_dict"] = True
                logger.info(f"Ultra-conservative memory: {gpu_memory_gb}GB GPU + {cpu_memory_gb}GB CPU offload")

        elif self.device == "auto" and torch.cuda.is_available():
            # Auto device with very conservative settings
            model_kwargs["device_map"] = "auto"
            model_kwargs["max_memory"] = {
                0: "2GB",  # Very conservative GPU usage
                "cpu": "16GB"
            }
            if self.settings.use_quantization:
                model_kwargs["offload_buffers"] = True
                logger.info("Auto device with ultra-conservative 2GB GPU limit")

        return model_kwargs

    def _get_quantization_config(self) -> Optional[BitsAndBytesConfig]:
        """Get quantization configuration for BitsAndBytes."""
        try:
            if self.settings.load_in_4bit:
                # Convert string dtype to torch dtype
                compute_dtype = getattr(torch, self.settings.bnb_4bit_compute_dtype, torch.float16)

                config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=compute_dtype,
                    bnb_4bit_quant_type=self.settings.bnb_4bit_quant_type,
                    bnb_4bit_use_double_quant=self.settings.bnb_4bit_use_double_quant,
                )

                # Add CPU offloading if enabled
                if self.settings.llm_int8_enable_fp32_cpu_offload:
                    config.llm_int8_enable_fp32_cpu_offload = True
                    logger.info("Enabled FP32 CPU offloading for 4-bit quantization")

                return config

            elif self.settings.load_in_8bit:
                config = BitsAndBytesConfig(
                    load_in_8bit=True,
                )

                # Add CPU offloading if enabled
                if self.settings.llm_int8_enable_fp32_cpu_offload:
                    config.llm_int8_enable_fp32_cpu_offload = True
                    logger.info("Enabled FP32 CPU offloading for 8-bit quantization")

                return config
            else:
                logger.warning("Quantization enabled but no valid quantization type specified")
                return None
        except Exception as e:
            logger.error(f"Failed to create quantization config: {e}")
            return None

    def _validate_model(self) -> None:
        """Validate that the loaded model is working correctly."""
        if self.model is None or self.tokenizer is None:
            raise ModelLoadError("Model or tokenizer is None after loading")

        try:
            # Test tokenization
            test_input = "Hello, world!"
            tokens = self.tokenizer(test_input, return_tensors="pt")

            if tokens.input_ids.numel() == 0:
                raise ModelLoadError("Tokenizer produced empty output")

            # Test model forward pass with a small input
            with torch.no_grad():
                if self.device != "cpu":
                    tokens = tokens.to(self.device)
                outputs = self.model(**tokens)

                if outputs.logits is None or outputs.logits.numel() == 0:
                    raise ModelLoadError("Model produced empty logits")

            logger.info("Model validation successful")

        except Exception as e:
            raise ModelLoadError(f"Model validation failed: {e}")

    def _load_processor(self) -> None:
        """Load processor for multimodal capabilities with error handling."""
        try:
            logger.info("Attempting to load processor for multimodal support...")
            self.processor = AutoProcessor.from_pretrained(
                self.settings.model_name,
                cache_dir=self.settings.model_cache_dir,
                trust_remote_code=True,
                token=self.settings.hf_token
            )
            self._processor_loaded = True
            logger.info("Processor loaded successfully - multimodal support enabled")

        except Exception as e:
            logger.warning(f"Could not load processor: {e}. Text-only mode will be used.")
            self._processor_loaded = False
            self.processor = None

    def _cleanup_partial_load(self) -> None:
        """Clean up partially loaded model components."""
        try:
            if self.model is not None:
                del self.model
                self.model = None

            if self.tokenizer is not None:
                del self.tokenizer
                self.tokenizer = None

            if self.processor is not None:
                del self.processor
                self.processor = None

            # Clear CUDA cache if using GPU
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()

            self._model_loaded = False
            self._processor_loaded = False

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def is_model_loaded(self) -> bool:
        """Check if the model is loaded."""
        return self._model_loaded and self.model is not None
    
    def is_processor_loaded(self) -> bool:
        """Check if the processor is loaded."""
        return self._processor_loaded and self.processor is not None
    
    def is_ready(self) -> bool:
        """Check if the model manager is ready for inference."""
        return self.is_model_loaded() and self.tokenizer is not None
    
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        repetition_penalty: float = 1.1,
        **kwargs
    ) -> str:
        """Generate text using the loaded model."""
        if not self.is_ready():
            raise ModelLoadError("Model is not ready for inference")
        
        try:
            # Run generation in thread pool to avoid blocking
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                self._generate_text_sync,
                prompt,
                max_tokens,
                temperature,
                top_p,
                top_k,
                repetition_penalty,
                kwargs
            )
            return result
            
        except Exception as e:
            logger.error(f"Text generation failed: {e}")
            raise ModelLoadError(f"Text generation failed: {str(e)}")
    
    def _generate_text_sync(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        top_p: float,
        top_k: int,
        repetition_penalty: float,
        kwargs: Dict[str, Any]
    ) -> str:
        """Generate text synchronously with robust error handling."""
        try:
            # Validate inputs
            if not prompt or not prompt.strip():
                raise ValueError("Empty prompt provided")

            if max_tokens <= 0 or max_tokens > self.settings.max_max_tokens:
                raise ValueError(f"Invalid max_tokens: {max_tokens}")

            if not (0.0 <= temperature <= 2.0):
                raise ValueError(f"Invalid temperature: {temperature}")

            if not (0.0 <= top_p <= 1.0):
                raise ValueError(f"Invalid top_p: {top_p}")

            # Tokenize input with error handling
            try:
                inputs = self.tokenizer(
                    prompt,
                    return_tensors="pt",
                    truncation=True,
                    max_length=self.settings.max_input_length,
                    padding=False
                )

                # Move to device
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

            except Exception as e:
                raise ModelLoadError(f"Tokenization failed: {e}")

            # Validate tokenized input
            if inputs["input_ids"].numel() == 0:
                raise ValueError("Tokenization produced empty input")

            input_length = inputs["input_ids"].shape[1]
            if input_length >= self.settings.max_input_length:
                logger.warning(f"Input truncated from {len(prompt)} chars to {input_length} tokens")

            # Configure generation parameters
            generation_kwargs = {
                "max_new_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "top_k": top_k,
                "repetition_penalty": repetition_penalty,
                "do_sample": temperature > 0.0,
                "pad_token_id": self.tokenizer.eos_token_id,
                "eos_token_id": self.tokenizer.eos_token_id,
                "use_cache": True,
                **kwargs
            }

            # Remove invalid parameters
            generation_kwargs = {k: v for k, v in generation_kwargs.items()
                               if v is not None and k not in ['kwargs']}

            # Generate with timeout and memory monitoring
            logger.debug(f"Generating text with {input_length} input tokens, max {max_tokens} new tokens")

            with torch.no_grad():
                try:
                    outputs = self.model.generate(
                        **inputs,
                        **generation_kwargs
                    )
                except RuntimeError as e:
                    if "out of memory" in str(e).lower():
                        # Clear cache and retry with smaller parameters
                        if self.device == "cuda":
                            torch.cuda.empty_cache()

                        # Reduce max_tokens and try again
                        reduced_tokens = min(max_tokens // 2, 512)
                        logger.warning(f"OOM error, retrying with {reduced_tokens} tokens")

                        generation_kwargs["max_new_tokens"] = reduced_tokens
                        outputs = self.model.generate(
                            **inputs,
                            **generation_kwargs
                        )
                    else:
                        raise ModelLoadError(f"Generation failed: {e}")

            # Validate outputs
            if outputs is None or outputs.numel() == 0:
                raise ModelLoadError("Model generated empty output")

            # Extract only the new tokens (excluding input)
            new_tokens = outputs[0][input_length:]

            if new_tokens.numel() == 0:
                logger.warning("Model generated no new tokens")
                return ""

            # Decode response with error handling
            try:
                response = self.tokenizer.decode(
                    new_tokens,
                    skip_special_tokens=True,
                    clean_up_tokenization_spaces=True
                )
            except Exception as e:
                raise ModelLoadError(f"Decoding failed: {e}")

            # Post-process response
            response = response.strip()

            # Validate response quality
            if not response:
                logger.warning("Generated empty response after decoding")
                return "I apologize, but I couldn't generate a proper response. Please try rephrasing your question."

            # Check for repetitive content
            if self._is_repetitive_response(response):
                logger.warning("Detected repetitive response")
                return "I notice my response might be repetitive. Could you please rephrase your question?"

            logger.debug(f"Generated {len(response)} characters")
            return response

        except Exception as e:
            logger.error(f"Text generation error: {e}")
            raise ModelLoadError(f"Text generation failed: {str(e)}")

    def _is_repetitive_response(self, text: str, threshold: float = 0.7) -> bool:
        """Check if response contains excessive repetition."""
        if len(text) < 50:  # Skip check for short responses
            return False

        words = text.split()
        if len(words) < 10:
            return False

        # Check for repeated phrases
        word_count = {}
        for word in words:
            word_lower = word.lower().strip('.,!?;:')
            word_count[word_lower] = word_count.get(word_lower, 0) + 1

        # Calculate repetition ratio
        total_words = len(words)
        unique_words = len(word_count)
        repetition_ratio = 1 - (unique_words / total_words)

        return repetition_ratio > threshold
    
    async def cleanup(self) -> None:
        """Clean up model resources."""
        try:
            logger.info("Cleaning up model manager...")
            
            if self.model is not None:
                del self.model
                self.model = None
                
            if self.tokenizer is not None:
                del self.tokenizer
                self.tokenizer = None
                
            if self.processor is not None:
                del self.processor
                self.processor = None
            
            # Clear CUDA cache if using GPU
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self._model_loaded = False
            self._processor_loaded = False
            
            logger.info("Model manager cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            "model_name": self.settings.model_name,
            "device": self.device,
            "model_loaded": self.is_model_loaded(),
            "processor_loaded": self.is_processor_loaded(),
            "ready": self.is_ready(),
            "cache_dir": self.settings.model_cache_dir,
        }
