"""
Input processor for handling multimodal inputs.

This module processes different types of inputs (text, images, audio)
and converts them into text format for the educational assistant.
"""

import base64
import io
import tempfile
from typing import Optional
from PIL import Image
import cv2
import numpy as np
from loguru import logger

from ..core.config import Settings
from ..core.models import InputContent, InputType
from ..core.exceptions import (
    ProcessingError,
    UnsupportedFileTypeError,
    FileTooLargeError,
    OCRError,
    AudioProcessingError
)


class InputProcessor:
    """Processes multimodal inputs for the educational assistant."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._ocr_engine = None
        self._speech_recognizer = None
    
    async def process_input(self, input_content: InputContent) -> str:
        """
        Process an input and return text representation.
        
        Args:
            input_content: The input content to process
            
        Returns:
            str: Text representation of the input
            
        Raises:
            ProcessingError: If processing fails
        """
        try:
            if input_content.type == InputType.TEXT:
                return await self._process_text(input_content)
            elif input_content.type == InputType.IMAGE:
                return await self._process_image(input_content)
            elif input_content.type == InputType.AUDIO:
                return await self._process_audio(input_content)
            else:
                raise UnsupportedFileTypeError(f"Unsupported input type: {input_content.type}")
                
        except Exception as e:
            logger.error(f"Failed to process {input_content.type} input: {e}")
            raise ProcessingError(f"Input processing failed: {str(e)}")
    
    async def _process_text(self, input_content: InputContent) -> str:
        """Process text input."""
        text = input_content.content.strip()
        
        # Validate text length
        if len(text) > self.settings.max_input_length:
            raise ProcessingError(f"Text input too long. Maximum length: {self.settings.max_input_length}")
        
        if not text:
            raise ProcessingError("Empty text input")
        
        return text
    
    async def _process_image(self, input_content: InputContent) -> str:
        """Process image input using OCR and image understanding with robust error handling."""
        try:
            # Validate base64 content
            if not input_content.content:
                raise ProcessingError("Empty image content provided")

            # Decode base64 image with validation
            try:
                image_data = base64.b64decode(input_content.content, validate=True)
            except Exception as e:
                raise ProcessingError(f"Invalid base64 image data: {e}")

            # Check file size
            file_size_mb = len(image_data) / (1024 * 1024)
            if file_size_mb > self.settings.max_file_size_mb:
                raise FileTooLargeError(
                    f"Image too large: {file_size_mb:.1f}MB. Maximum size: {self.settings.max_file_size_mb}MB"
                )

            # Validate MIME type
            if input_content.mime_type and input_content.mime_type not in self.settings.supported_image_types:
                raise UnsupportedFileTypeError(f"Unsupported image type: {input_content.mime_type}")

            # Load and validate image
            image = await self._load_and_validate_image(image_data)

            # Extract text using OCR with error handling
            ocr_text = await self._extract_text_from_image(image)

            # Analyze image content
            image_description = await self._analyze_image_content(image)

            # Combine results with validation
            result_parts = []

            if ocr_text and ocr_text.strip():
                # Clean and validate OCR text
                cleaned_ocr = self._clean_ocr_text(ocr_text)
                if cleaned_ocr:
                    result_parts.append(f"Text found in image: {cleaned_ocr}")

            if image_description and image_description.strip():
                result_parts.append(f"Image content: {image_description}")

            if not result_parts:
                result_parts.append("Image uploaded (no text or recognizable educational content detected)")

            result = " | ".join(result_parts)

            # Validate final result length
            if len(result) > self.settings.max_input_length:
                result = result[:self.settings.max_input_length] + "... [truncated]"

            return result

        except (ProcessingError, FileTooLargeError, UnsupportedFileTypeError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            raise OCRError(f"Failed to process image: {str(e)}")

    async def _load_and_validate_image(self, image_data: bytes) -> Image.Image:
        """Load and validate image data."""
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))

            # Validate image
            if image.size[0] == 0 or image.size[1] == 0:
                raise ProcessingError("Invalid image dimensions")

            # Check image size limits
            max_dimension = 4096  # Maximum dimension in pixels
            if image.size[0] > max_dimension or image.size[1] > max_dimension:
                logger.warning(f"Large image detected: {image.size}, resizing...")
                image.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)

            # Convert to RGB if necessary
            if image.mode not in ['RGB', 'L']:  # RGB or grayscale
                logger.info(f"Converting image from {image.mode} to RGB")
                image = image.convert('RGB')

            # Verify image is not corrupted
            image.verify()

            # Reload image after verify (verify() can only be called once)
            image = Image.open(io.BytesIO(image_data))
            if image.mode not in ['RGB', 'L']:
                image = image.convert('RGB')

            return image

        except Exception as e:
            raise ProcessingError(f"Failed to load image: {e}")

    def _clean_ocr_text(self, text: str) -> str:
        """Clean and validate OCR text output."""
        if not text:
            return ""

        # Remove excessive whitespace
        cleaned = " ".join(text.split())

        # Remove non-printable characters except common ones
        import string
        printable_chars = string.printable + "áéíóúñü"  # Add common accented characters
        cleaned = "".join(char for char in cleaned if char in printable_chars)

        # Remove very short "words" that are likely OCR artifacts
        words = cleaned.split()
        filtered_words = [word for word in words if len(word) > 1 or word.isalnum()]

        result = " ".join(filtered_words)

        # Only return if we have meaningful content
        if len(result) < 3:  # Too short to be meaningful
            return ""

        return result
    
    async def _extract_text_from_image(self, image: Image.Image) -> str:
        """Extract text from image using OCR with robust error handling."""
        try:
            # Initialize OCR engine if not already done
            if self._ocr_engine is None:
                await self._initialize_ocr()

            # Validate image
            if image is None or image.size[0] == 0 or image.size[1] == 0:
                logger.warning("Invalid image for OCR")
                return ""

            # Convert PIL image to numpy array for OCR
            try:
                image_array = np.array(image)
                if image_array.size == 0:
                    logger.warning("Empty image array for OCR")
                    return ""
            except Exception as e:
                logger.warning(f"Failed to convert image to array: {e}")
                return ""

            # Perform OCR based on engine
            if self.settings.ocr_engine == "easyocr" and self._ocr_engine != "tesseract":
                return await self._extract_with_easyocr(image_array)
            else:
                return await self._extract_with_tesseract(image)

        except Exception as e:
            logger.warning(f"OCR extraction failed: {e}")
            return ""  # Return empty string instead of failing

    async def _extract_with_easyocr(self, image_array: np.ndarray) -> str:
        """Extract text using EasyOCR with error handling."""
        try:
            # Use EasyOCR with confidence threshold
            results = self._ocr_engine.readtext(
                image_array,
                detail=1,  # Return bounding boxes and confidence
                paragraph=False,  # Don't group into paragraphs
                width_ths=0.7,  # Width threshold for text detection
                height_ths=0.7  # Height threshold for text detection
            )

            # Filter results by confidence and clean text
            confidence_threshold = 0.5
            text_parts = []

            for result in results:
                if len(result) >= 3:  # [bbox, text, confidence]
                    text, confidence = result[1], result[2]
                    if confidence > confidence_threshold and text.strip():
                        # Clean the text
                        cleaned_text = text.strip()
                        if len(cleaned_text) > 1:  # Ignore single characters
                            text_parts.append(cleaned_text)

            if not text_parts:
                logger.debug("EasyOCR found no text with sufficient confidence")
                return ""

            # Join text parts with spaces
            result_text = " ".join(text_parts)
            logger.debug(f"EasyOCR extracted {len(result_text)} characters")
            return result_text

        except Exception as e:
            logger.warning(f"EasyOCR extraction failed: {e}")
            return ""

    async def _extract_with_tesseract(self, image: Image.Image) -> str:
        """Extract text using Tesseract with error handling."""
        try:
            import pytesseract

            # Configure Tesseract options for better accuracy
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?;:()[]{}+-=*/\n'

            # Extract text
            text = pytesseract.image_to_string(
                image,
                config=custom_config,
                lang="+".join(self.settings.ocr_languages)
            )

            if not text or not text.strip():
                logger.debug("Tesseract found no text")
                return ""

            # Clean the text
            cleaned_text = text.strip()
            logger.debug(f"Tesseract extracted {len(cleaned_text)} characters")
            return cleaned_text

        except ImportError:
            logger.warning("Tesseract not available")
            return ""
        except Exception as e:
            logger.warning(f"Tesseract extraction failed: {e}")
            return ""
    
    async def _analyze_image_content(self, image: Image.Image) -> str:
        """Analyze image content for educational context."""
        try:
            # This is a placeholder for more sophisticated image analysis
            # In a full implementation, you might use:
            # - Computer vision models to detect educational content
            # - Chart/diagram recognition
            # - Mathematical equation detection
            # - Scientific diagram analysis
            
            # For now, provide basic image characteristics
            width, height = image.size
            
            # Simple heuristics for educational content
            aspect_ratio = width / height
            
            if aspect_ratio > 2:
                return "Wide image (possibly a chart, timeline, or diagram)"
            elif aspect_ratio < 0.5:
                return "Tall image (possibly a document page or list)"
            elif 0.8 <= aspect_ratio <= 1.2:
                return "Square image (possibly a diagram, equation, or illustration)"
            else:
                return "Rectangular image (possibly educational content)"
                
        except Exception as e:
            logger.warning(f"Image analysis failed: {e}")
            return "Educational image"
    
    async def _process_audio(self, input_content: InputContent) -> str:
        """Process audio input using speech-to-text with robust error handling."""
        try:
            # Validate base64 content
            if not input_content.content:
                raise ProcessingError("Empty audio content provided")

            # Decode base64 audio with validation
            try:
                audio_data = base64.b64decode(input_content.content, validate=True)
            except Exception as e:
                raise ProcessingError(f"Invalid base64 audio data: {e}")

            # Check file size
            file_size_mb = len(audio_data) / (1024 * 1024)
            if file_size_mb > self.settings.max_file_size_mb:
                raise FileTooLargeError(
                    f"Audio too large: {file_size_mb:.1f}MB. Maximum size: {self.settings.max_file_size_mb}MB"
                )

            # Validate minimum file size (avoid empty files)
            if len(audio_data) < 1024:  # Less than 1KB
                raise ProcessingError("Audio file too small to contain meaningful content")

            # Validate MIME type
            if input_content.mime_type and input_content.mime_type not in self.settings.supported_audio_types:
                raise UnsupportedFileTypeError(f"Unsupported audio type: {input_content.mime_type}")

            # Initialize speech recognizer if not already done
            if self._speech_recognizer is None:
                await self._initialize_speech_recognition()

            # Convert audio to text with multiple attempts
            transcription = await self._transcribe_audio_robust(audio_data, input_content.mime_type)

            # Validate and clean transcription
            if not transcription or not transcription.strip():
                return "Audio uploaded (no speech detected or transcription failed)"

            # Clean transcription
            cleaned_transcription = self._clean_transcription(transcription)

            if not cleaned_transcription:
                return "Audio uploaded (transcription produced no meaningful content)"

            # Validate transcription length
            if len(cleaned_transcription) > self.settings.max_input_length:
                cleaned_transcription = cleaned_transcription[:self.settings.max_input_length] + "... [truncated]"

            return f"Audio transcription: {cleaned_transcription}"

        except (ProcessingError, FileTooLargeError, UnsupportedFileTypeError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            logger.error(f"Audio processing failed: {e}")
            raise AudioProcessingError(f"Failed to process audio: {str(e)}")

    def _clean_transcription(self, text: str) -> str:
        """Clean and validate transcription text."""
        if not text:
            return ""

        # Remove excessive whitespace
        cleaned = " ".join(text.split())

        # Remove common transcription artifacts
        artifacts = [
            "[Music]", "[Applause]", "[Laughter]", "[Noise]",
            "[Inaudible]", "[Background noise]", "[Static]"
        ]

        for artifact in artifacts:
            cleaned = cleaned.replace(artifact, "")

        # Remove very short transcriptions that are likely errors
        if len(cleaned.strip()) < 3:
            return ""

        return cleaned.strip()
    
    async def _initialize_ocr(self) -> None:
        """Initialize OCR engine."""
        try:
            if self.settings.ocr_engine == "easyocr":
                import easyocr
                self._ocr_engine = easyocr.Reader(self.settings.ocr_languages)
            else:
                # Tesseract doesn't need initialization
                self._ocr_engine = "tesseract"
                
        except ImportError as e:
            logger.error(f"OCR engine not available: {e}")
            raise ProcessingError("OCR functionality not available")
    
    async def _initialize_speech_recognition(self) -> None:
        """Initialize speech recognition."""
        try:
            import speech_recognition as sr
            self._speech_recognizer = sr.Recognizer()
            
        except ImportError as e:
            logger.error(f"Speech recognition not available: {e}")
            raise ProcessingError("Speech recognition functionality not available")
    
    async def _transcribe_audio_robust(self, audio_data: bytes, mime_type: str) -> str:
        """Transcribe audio to text with multiple fallback methods."""
        transcription_methods = [
            self._transcribe_with_google,
            self._transcribe_with_sphinx,
            self._transcribe_with_basic_processing
        ]

        for method in transcription_methods:
            try:
                result = await method(audio_data, mime_type)
                if result and result.strip():
                    logger.debug(f"Transcription successful with {method.__name__}")
                    return result
            except Exception as e:
                logger.warning(f"Transcription method {method.__name__} failed: {e}")
                continue

        logger.warning("All transcription methods failed")
        return ""

    async def _transcribe_with_google(self, audio_data: bytes, mime_type: str) -> str:
        """Transcribe using Google Speech Recognition."""
        try:
            import speech_recognition as sr
            from pydub import AudioSegment

            # Process audio with validation
            processed_audio = await self._process_audio_file(audio_data, mime_type)
            if not processed_audio:
                return ""

            # Save processed audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as wav_file:
                processed_audio.export(wav_file.name, format="wav")
                wav_path = wav_file.name

            try:
                # Transcribe using speech recognition
                with sr.AudioFile(wav_path) as source:
                    # Adjust for ambient noise
                    self._speech_recognizer.adjust_for_ambient_noise(source, duration=0.5)
                    audio_sr = self._speech_recognizer.record(source)

                # Try Google Speech Recognition
                result = self._speech_recognizer.recognize_google(
                    audio_sr,
                    language="en-US",
                    show_all=False
                )

                return result if result else ""

            finally:
                # Clean up temporary file
                try:
                    import os
                    os.unlink(wav_path)
                except:
                    pass

        except ImportError:
            logger.warning("Google Speech Recognition not available")
            return ""
        except Exception as e:
            logger.warning(f"Google transcription failed: {e}")
            return ""

    async def _transcribe_with_sphinx(self, audio_data: bytes, mime_type: str) -> str:
        """Transcribe using offline Sphinx recognition."""
        try:
            import speech_recognition as sr

            # Process audio
            processed_audio = await self._process_audio_file(audio_data, mime_type)
            if not processed_audio:
                return ""

            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as wav_file:
                processed_audio.export(wav_file.name, format="wav")
                wav_path = wav_file.name

            try:
                # Transcribe using Sphinx
                with sr.AudioFile(wav_path) as source:
                    audio_sr = self._speech_recognizer.record(source)

                result = self._speech_recognizer.recognize_sphinx(audio_sr)
                return result if result else ""

            finally:
                # Clean up
                try:
                    import os
                    os.unlink(wav_path)
                except:
                    pass

        except ImportError:
            logger.warning("Sphinx recognition not available")
            return ""
        except Exception as e:
            logger.warning(f"Sphinx transcription failed: {e}")
            return ""

    async def _transcribe_with_basic_processing(self, audio_data: bytes, mime_type: str) -> str:
        """Basic audio processing fallback."""
        try:
            # This is a placeholder for basic audio analysis
            # In a real implementation, you might use simpler audio processing
            # or return a generic message about audio content

            processed_audio = await self._process_audio_file(audio_data, mime_type)
            if processed_audio and len(processed_audio) > 1000:  # Has some content
                duration = len(processed_audio) / 1000.0  # Duration in seconds
                return f"Audio content detected (duration: {duration:.1f} seconds)"

            return ""

        except Exception as e:
            logger.warning(f"Basic audio processing failed: {e}")
            return ""

    async def _process_audio_file(self, audio_data: bytes, mime_type: str) -> Optional['AudioSegment']:
        """Process and validate audio file."""
        try:
            from pydub import AudioSegment

            # Save to temporary file first
            with tempfile.NamedTemporaryFile(suffix=self._get_audio_extension(mime_type)) as temp_file:
                temp_file.write(audio_data)
                temp_file.flush()

                # Load audio
                audio = AudioSegment.from_file(temp_file.name)

                # Validate audio
                if len(audio) == 0:
                    logger.warning("Audio file has zero duration")
                    return None

                # Check duration limits
                max_duration_ms = self.settings.audio_chunk_length * 1000  # Convert to milliseconds
                if len(audio) > max_duration_ms:
                    logger.warning(f"Audio too long ({len(audio)/1000:.1f}s), truncating to {max_duration_ms/1000:.1f}s")
                    audio = audio[:max_duration_ms]

                # Normalize audio settings
                audio = audio.set_frame_rate(self.settings.audio_sample_rate)
                audio = audio.set_channels(1)  # Convert to mono

                # Normalize volume
                if audio.max_possible_amplitude > 0:
                    audio = audio.normalize()

                return audio

        except ImportError:
            logger.warning("pydub not available for audio processing")
            return None
        except Exception as e:
            logger.warning(f"Audio processing failed: {e}")
            return None
    
    def _get_audio_extension(self, mime_type: str) -> str:
        """Get file extension from MIME type."""
        mime_to_ext = {
            "audio/wav": ".wav",
            "audio/mp3": ".mp3",
            "audio/mpeg": ".mp3",
            "audio/m4a": ".m4a",
            "audio/ogg": ".ogg",
        }
        return mime_to_ext.get(mime_type, ".wav")
