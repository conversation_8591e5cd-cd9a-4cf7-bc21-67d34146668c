"""
Chat service for educational assistance.

This module handles the main chat functionality, including conversation
management, prompt engineering, and response generation.
"""

from uuid import UUID
from typing import List, Tuple, Optional, Dict, Any
from loguru import logger

from ..core.config import Settings
from ..core.models import EducationalLevel, Subject
from ..core.exceptions import ModelGenerationError
from .model_manager import ModelManager


class ChatService:
    """Handles chat functionality for educational assistance."""
    
    def __init__(self, model_manager: ModelManager, settings: Settings):
        self.model_manager = model_manager
        self.settings = settings
        
        # In-memory conversation storage (replace with database in production)
        self.conversations: Dict[UUID, List[Dict[str, Any]]] = {}
    
    async def generate_response(
        self,
        session_id: UUID,
        content: str,
        educational_level: EducationalLevel,
        subject: Subject,
        max_tokens: int = 1024,
        temperature: float = 0.7,
        include_reasoning: bool = False
    ) -> Tuple[str, Optional[List[str]], List[str]]:
        """
        Generate an educational response with robust error handling.

        Args:
            session_id: Session identifier
            content: User input content
            educational_level: Educational level for age-appropriate responses
            subject: Academic subject
            max_tokens: Maximum tokens in response
            temperature: Response creativity
            include_reasoning: Whether to include step-by-step reasoning

        Returns:
            Tuple of (response_text, reasoning_steps, follow_up_questions)
        """
        try:
            # Validate inputs
            if not content or not content.strip():
                raise ModelGenerationError("Empty content provided for response generation")

            if not session_id:
                raise ModelGenerationError("Invalid session ID provided")

            # Validate parameters
            max_tokens = max(1, min(max_tokens, self.settings.max_max_tokens))
            temperature = max(0.0, min(temperature, 2.0))

            # Check model manager readiness
            if not self.model_manager.is_ready():
                raise ModelGenerationError("Model is not ready for response generation")

            # Get conversation history with validation
            conversation_history = self._get_validated_conversation_history(session_id)

            # Build the prompt with error handling
            try:
                prompt = self._build_educational_prompt(
                    content=content,
                    educational_level=educational_level,
                    subject=subject,
                    conversation_history=conversation_history,
                    include_reasoning=include_reasoning
                )
            except Exception as e:
                logger.error(f"Failed to build prompt: {e}")
                raise ModelGenerationError(f"Prompt building failed: {str(e)}")

            # Validate prompt length
            if len(prompt) > self.settings.max_input_length * 4:  # Rough character estimate
                logger.warning("Prompt is very long, truncating conversation history")
                # Retry with shorter history
                conversation_history = conversation_history[-2:] if conversation_history else []
                prompt = self._build_educational_prompt(
                    content=content,
                    educational_level=educational_level,
                    subject=subject,
                    conversation_history=conversation_history,
                    include_reasoning=include_reasoning
                )

            # Generate response with retry logic
            response = await self._generate_with_retry(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                max_retries=2
            )

            # Validate response
            if not response or not response.strip():
                logger.warning("Model generated empty response")
                response = self._get_fallback_response(educational_level, subject)

            # Parse response for reasoning and follow-ups
            response_text, reasoning_steps, follow_ups = self._parse_response(
                response, include_reasoning
            )

            # Validate parsed response
            if not response_text or not response_text.strip():
                response_text = self._get_fallback_response(educational_level, subject)
                reasoning_steps = None
                follow_ups = []

            # Update conversation history
            try:
                self._update_conversation_history(
                    session_id, content, response_text, educational_level, subject
                )
            except Exception as e:
                logger.warning(f"Failed to update conversation history: {e}")
                # Don't fail the request for history update issues

            return response_text, reasoning_steps, follow_ups

        except ModelGenerationError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            logger.error(f"Unexpected error in response generation: {e}")
            # Return a safe fallback response
            fallback_response = self._get_fallback_response(educational_level, subject)
            return fallback_response, None, []

    def _get_validated_conversation_history(self, session_id: UUID) -> List[Dict[str, Any]]:
        """Get and validate conversation history."""
        try:
            history = self.conversations.get(session_id, [])

            # Validate history entries
            validated_history = []
            for entry in history:
                if isinstance(entry, dict) and "role" in entry and "content" in entry:
                    # Ensure content is not too long
                    content = entry.get("content", "")
                    if len(content) > 1000:  # Truncate very long entries
                        content = content[:1000] + "..."
                        entry = entry.copy()
                        entry["content"] = content
                    validated_history.append(entry)

            return validated_history

        except Exception as e:
            logger.warning(f"Error validating conversation history: {e}")
            return []

    async def _generate_with_retry(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        max_retries: int = 2
    ) -> str:
        """Generate response with retry logic."""
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                # Adjust parameters for retry attempts
                retry_max_tokens = max_tokens if attempt == 0 else max(max_tokens // 2, 256)
                retry_temperature = temperature if attempt == 0 else min(temperature + 0.1, 1.0)

                logger.debug(f"Generation attempt {attempt + 1}/{max_retries + 1}")

                response = await self.model_manager.generate_text(
                    prompt=prompt,
                    max_tokens=retry_max_tokens,
                    temperature=retry_temperature,
                    top_p=self.settings.default_top_p,
                    top_k=self.settings.default_top_k,
                    repetition_penalty=self.settings.default_repetition_penalty
                )

                if response and response.strip():
                    return response
                else:
                    logger.warning(f"Empty response on attempt {attempt + 1}")

            except Exception as e:
                last_error = e
                logger.warning(f"Generation attempt {attempt + 1} failed: {e}")

                if attempt < max_retries:
                    # Wait a bit before retry
                    import asyncio
                    await asyncio.sleep(0.5)

        # All attempts failed
        if last_error:
            raise ModelGenerationError(f"All generation attempts failed. Last error: {str(last_error)}")
        else:
            raise ModelGenerationError("All generation attempts produced empty responses")

    def _get_fallback_response(self, educational_level: EducationalLevel, subject: Subject) -> str:
        """Get a safe fallback response when generation fails."""
        level_appropriate_responses = {
            EducationalLevel.ELEMENTARY: "I'm having trouble right now, but I'd love to help you learn! Can you try asking your question in a different way?",
            EducationalLevel.MIDDLE_SCHOOL: "I'm experiencing some technical difficulties. Could you please rephrase your question so I can better assist you with your studies?",
            EducationalLevel.HIGH_SCHOOL: "I'm currently having some issues generating a proper response. Please try rephrasing your question, and I'll do my best to help you understand the topic.",
            EducationalLevel.COLLEGE: "I'm experiencing technical difficulties at the moment. Please rephrase your question, and I'll provide you with a comprehensive educational response.",
            EducationalLevel.GRADUATE: "I'm currently unable to generate an appropriate response due to technical issues. Please reformulate your query, and I'll provide detailed academic assistance.",
            EducationalLevel.ADULT_LEARNING: "I'm having some technical difficulties right now. Please try asking your question differently, and I'll help you with your learning goals."
        }

        return level_appropriate_responses.get(
            educational_level,
            "I'm having some technical difficulties. Please try rephrasing your question, and I'll do my best to help you learn!"
        )
    
    def _build_educational_prompt(
        self,
        content: str,
        educational_level: EducationalLevel,
        subject: Subject,
        conversation_history: List[Dict[str, Any]],
        include_reasoning: bool
    ) -> str:
        """Build an educational prompt for the model."""
        
        # Base system prompt
        system_prompt = self.settings.educational_system_prompt
        
        # Add level-specific instructions
        level_instructions = self._get_level_specific_instructions(educational_level)
        
        # Add subject-specific instructions
        subject_instructions = self._get_subject_specific_instructions(subject)
        
        # Add reasoning instructions if requested
        reasoning_instructions = ""
        if include_reasoning:
            reasoning_instructions = """
When solving problems or explaining concepts, please:
1. Break down your explanation into clear, numbered steps
2. Show your reasoning process
3. Explain why each step is necessary
4. Use the format: "Step 1: [explanation]", "Step 2: [explanation]", etc.
"""
        
        # Build conversation context
        conversation_context = ""
        if conversation_history:
            conversation_context = "\n\nPrevious conversation:\n"
            for msg in conversation_history[-5:]:  # Last 5 messages for context
                role = msg.get("role", "user")
                text = msg.get("content", "")
                conversation_context += f"{role.title()}: {text}\n"
        
        # Combine all parts
        full_prompt = f"""{system_prompt}

{level_instructions}

{subject_instructions}

{reasoning_instructions}

{conversation_context}

Current question/input: {content}

Please provide a helpful, educational response that:
1. Directly addresses the question or input
2. Is appropriate for the {educational_level.value.replace('_', ' ')} level
3. Focuses on {subject.value} if relevant
4. Encourages learning and understanding
5. Provides clear explanations
6. Suggests follow-up questions or related topics to explore

Response:"""
        
        return full_prompt
    
    def _get_level_specific_instructions(self, level: EducationalLevel) -> str:
        """Get instructions specific to educational level."""
        instructions = {
            EducationalLevel.ELEMENTARY: """
Adjust your language and explanations for elementary school students (ages 5-11):
- Use simple, clear language
- Provide concrete examples and analogies
- Break complex ideas into very small steps
- Use encouraging and positive tone
- Avoid abstract concepts unless necessary
- Include visual descriptions when helpful
""",
            EducationalLevel.MIDDLE_SCHOOL: """
Adjust your language and explanations for middle school students (ages 11-14):
- Use age-appropriate vocabulary with some academic terms
- Provide relatable examples and analogies
- Introduce more abstract thinking gradually
- Encourage questions and exploration
- Connect concepts to real-world applications
""",
            EducationalLevel.HIGH_SCHOOL: """
Adjust your language and explanations for high school students (ages 14-18):
- Use academic vocabulary and terminology
- Provide detailed explanations with examples
- Encourage critical thinking and analysis
- Connect concepts across different subjects
- Prepare students for college-level thinking
""",
            EducationalLevel.COLLEGE: """
Adjust your language and explanations for college students (ages 18+):
- Use advanced academic vocabulary
- Provide comprehensive explanations
- Encourage independent research and critical analysis
- Reference academic sources and methodologies
- Challenge students to think deeply about concepts
""",
            EducationalLevel.GRADUATE: """
Adjust your language and explanations for graduate students:
- Use specialized academic and professional terminology
- Provide in-depth, research-level explanations
- Encourage original thinking and research
- Reference current academic literature and debates
- Support advanced analytical and synthesis skills
""",
            EducationalLevel.ADULT_LEARNING: """
Adjust your language and explanations for adult learners:
- Respect prior life experience and knowledge
- Connect new concepts to practical applications
- Provide flexible learning approaches
- Acknowledge different learning styles and paces
- Focus on practical utility and real-world relevance
"""
        }
        return instructions.get(level, instructions[EducationalLevel.HIGH_SCHOOL])
    
    def _get_subject_specific_instructions(self, subject: Subject) -> str:
        """Get instructions specific to academic subject."""
        instructions = {
            Subject.MATHEMATICS: """
For mathematics questions:
- Show step-by-step calculations
- Explain mathematical reasoning
- Use proper mathematical notation
- Provide alternative solution methods when applicable
- Check answers and explain verification
""",
            Subject.SCIENCE: """
For science questions:
- Use the scientific method approach
- Explain underlying principles and theories
- Provide real-world examples and applications
- Encourage observation and experimentation
- Connect concepts to current scientific understanding
""",
            Subject.PHYSICS: """
For physics questions:
- Explain physical principles and laws
- Show mathematical derivations when appropriate
- Use diagrams and visual descriptions
- Connect theory to practical applications
- Emphasize problem-solving strategies
""",
            Subject.CHEMISTRY: """
For chemistry questions:
- Explain chemical principles and reactions
- Use proper chemical notation and formulas
- Describe molecular-level processes
- Connect to everyday chemical phenomena
- Emphasize safety and proper procedures
""",
            Subject.BIOLOGY: """
For biology questions:
- Explain biological processes and systems
- Use proper scientific terminology
- Connect structure to function
- Relate to human health and environment
- Encourage observation of living systems
""",
            Subject.HISTORY: """
For history questions:
- Provide historical context and background
- Explain cause-and-effect relationships
- Use primary and secondary sources
- Encourage critical analysis of events
- Connect past events to present understanding
""",
            Subject.ENGLISH: """
For English/Language Arts questions:
- Focus on reading comprehension and analysis
- Explain literary devices and techniques
- Improve writing and communication skills
- Encourage critical thinking about texts
- Develop vocabulary and language skills
""",
            Subject.GENERAL: """
For general educational questions:
- Identify the most relevant subject area
- Provide interdisciplinary connections
- Encourage broad thinking and exploration
- Adapt approach based on question content
- Foster curiosity and lifelong learning
"""
        }
        return instructions.get(subject, instructions[Subject.GENERAL])
    
    def _parse_response(
        self, 
        response: str, 
        include_reasoning: bool
    ) -> Tuple[str, Optional[List[str]], List[str]]:
        """Parse the model response to extract reasoning and follow-ups."""
        
        reasoning_steps = None
        follow_ups = []
        
        # Extract reasoning steps if requested
        if include_reasoning:
            reasoning_steps = self._extract_reasoning_steps(response)
        
        # Extract follow-up questions
        follow_ups = self._extract_follow_ups(response)
        
        # Clean the main response
        response_text = self._clean_response_text(response)
        
        return response_text, reasoning_steps, follow_ups
    
    def _extract_reasoning_steps(self, response: str) -> List[str]:
        """Extract step-by-step reasoning from response."""
        import re
        
        # Look for numbered steps
        step_pattern = r'Step \d+:?\s*([^Step]+?)(?=Step \d+|$)'
        steps = re.findall(step_pattern, response, re.IGNORECASE | re.DOTALL)
        
        if steps:
            return [step.strip() for step in steps]
        
        # Fallback: look for numbered lists
        numbered_pattern = r'\d+\.\s*([^\d]+?)(?=\d+\.|$)'
        numbered_steps = re.findall(numbered_pattern, response, re.DOTALL)
        
        if numbered_steps:
            return [step.strip() for step in numbered_steps]
        
        return []
    
    def _extract_follow_ups(self, response: str) -> List[str]:
        """Extract follow-up questions from response."""
        # Simple extraction of questions ending with ?
        import re
        
        # Look for sentences ending with question marks
        questions = re.findall(r'([^.!?]*\?)', response)
        
        # Filter and clean questions
        follow_ups = []
        for q in questions:
            q = q.strip()
            if len(q) > 10 and any(word in q.lower() for word in 
                                 ['would you like', 'want to', 'interested in', 'explore', 'learn more']):
                follow_ups.append(q)
        
        # Limit to 3 follow-ups
        return follow_ups[:3]
    
    def _clean_response_text(self, response: str) -> str:
        """Clean and format the response text."""
        # Remove any system artifacts or unwanted patterns
        cleaned = response.strip()
        
        # Remove common artifacts
        artifacts = [
            "Response:",
            "Assistant:",
            "AI:",
            "Here's my response:",
            "Here is my response:",
        ]
        
        for artifact in artifacts:
            if cleaned.startswith(artifact):
                cleaned = cleaned[len(artifact):].strip()
        
        return cleaned
    
    def _update_conversation_history(
        self,
        session_id: UUID,
        user_input: str,
        assistant_response: str,
        educational_level: EducationalLevel,
        subject: Subject
    ) -> None:
        """Update conversation history for the session."""
        if session_id not in self.conversations:
            self.conversations[session_id] = []
        
        conversation = self.conversations[session_id]
        
        # Add user message
        conversation.append({
            "role": "user",
            "content": user_input,
            "educational_level": educational_level.value,
            "subject": subject.value
        })
        
        # Add assistant response
        conversation.append({
            "role": "assistant", 
            "content": assistant_response,
            "educational_level": educational_level.value,
            "subject": subject.value
        })
        
        # Limit conversation history to prevent memory issues
        max_history = self.settings.max_conversation_length
        if len(conversation) > max_history:
            # Keep the most recent messages
            self.conversations[session_id] = conversation[-max_history:]
    
    def get_conversation_history(self, session_id: UUID) -> List[Dict[str, Any]]:
        """Get conversation history for a session."""
        return self.conversations.get(session_id, [])
    
    def clear_conversation_history(self, session_id: UUID) -> None:
        """Clear conversation history for a session."""
        if session_id in self.conversations:
            del self.conversations[session_id]
