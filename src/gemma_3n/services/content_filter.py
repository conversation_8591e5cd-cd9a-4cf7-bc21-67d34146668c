"""
Content filter for educational appropriateness.

This module filters content to ensure it's appropriate for educational
assistance and blocks non-educational queries.
"""

import re
from typing import List, Set
from loguru import logger

from ..core.config import Settings
from ..core.models import Subject


class ContentFilter:
    """Filters content for educational appropriateness."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        
        # Educational keywords that indicate academic content
        self.educational_keywords = {
            "math", "mathematics", "algebra", "geometry", "calculus", "statistics",
            "science", "physics", "chemistry", "biology", "anatomy", "ecology",
            "history", "geography", "literature", "english", "grammar", "writing",
            "reading", "vocabulary", "spelling", "pronunciation", "language",
            "computer science", "programming", "coding", "algorithm", "data structure",
            "art", "music", "theory", "composition", "drawing", "painting",
            "study", "homework", "assignment", "project", "research", "essay",
            "exam", "test", "quiz", "grade", "school", "college", "university",
            "learn", "learning", "teach", "teaching", "education", "academic",
            "student", "teacher", "professor", "tutor", "lesson", "course",
            "subject", "topic", "concept", "theory", "principle", "formula",
            "equation", "problem", "solution", "answer", "explanation", "example",
            "definition", "meaning", "understand", "comprehend", "analyze",
            "evaluate", "synthesize", "compare", "contrast", "summarize",
            "explain", "describe", "identify", "classify", "categorize",
            "calculate", "solve", "prove", "demonstrate", "illustrate",
            "diagram", "chart", "graph", "table", "figure", "image",
            "book", "textbook", "article", "paper", "journal", "reference",
            "citation", "bibliography", "source", "evidence", "fact",
            "knowledge", "information", "data", "research", "study"
        }
        
        # Subject-specific keywords
        self.subject_keywords = {
            Subject.MATHEMATICS: {
                "number", "integer", "fraction", "decimal", "percent", "ratio",
                "proportion", "equation", "inequality", "function", "variable",
                "constant", "coefficient", "exponent", "logarithm", "trigonometry",
                "sine", "cosine", "tangent", "derivative", "integral", "limit",
                "matrix", "vector", "probability", "permutation", "combination"
            },
            Subject.SCIENCE: {
                "experiment", "hypothesis", "theory", "law", "observation",
                "measurement", "data", "analysis", "conclusion", "variable",
                "control", "method", "procedure", "result", "evidence"
            },
            Subject.PHYSICS: {
                "force", "energy", "motion", "velocity", "acceleration", "mass",
                "weight", "gravity", "friction", "momentum", "wave", "frequency",
                "amplitude", "light", "sound", "electricity", "magnetism",
                "atom", "molecule", "particle", "quantum", "relativity"
            },
            Subject.CHEMISTRY: {
                "element", "compound", "molecule", "atom", "ion", "bond",
                "reaction", "equation", "catalyst", "acid", "base", "pH",
                "oxidation", "reduction", "periodic table", "electron",
                "proton", "neutron", "isotope", "mole", "molarity"
            },
            Subject.BIOLOGY: {
                "cell", "organism", "species", "evolution", "genetics", "DNA",
                "RNA", "protein", "enzyme", "metabolism", "photosynthesis",
                "respiration", "reproduction", "development", "ecosystem",
                "biodiversity", "classification", "taxonomy", "anatomy",
                "physiology", "behavior", "adaptation", "natural selection"
            },
            Subject.HISTORY: {
                "timeline", "chronology", "era", "period", "civilization",
                "culture", "society", "government", "politics", "war",
                "revolution", "empire", "dynasty", "democracy", "monarchy",
                "constitution", "treaty", "document", "artifact", "primary source"
            },
            Subject.ENGLISH: {
                "grammar", "syntax", "vocabulary", "spelling", "punctuation",
                "sentence", "paragraph", "essay", "story", "poem", "novel",
                "character", "plot", "theme", "setting", "metaphor", "simile",
                "alliteration", "rhyme", "meter", "genre", "author", "narrator"
            }
        }
        
        # Non-educational keywords that should be blocked
        self.non_educational_keywords = {
            "hack", "cheat", "illegal", "drugs", "violence", "weapon",
            "adult content", "inappropriate", "offensive", "hate",
            "discrimination", "harassment", "bullying", "threat",
            "personal information", "private", "confidential", "password",
            "credit card", "social security", "bank account", "financial",
            "gambling", "betting", "casino", "lottery", "investment advice",
            "medical diagnosis", "medical treatment", "prescription",
            "legal advice", "lawsuit", "court", "attorney", "lawyer"
        }
        
        # Question patterns that indicate educational intent
        self.educational_patterns = [
            r"how do (?:i|you) (?:solve|calculate|find|determine)",
            r"what is (?:the|a) (?:definition|meaning|formula|equation)",
            r"explain (?:the|this|how|why|what)",
            r"can you help me (?:with|understand|learn)",
            r"i (?:don't|do not) understand",
            r"homework|assignment|project|study",
            r"(?:math|science|history|english|physics|chemistry|biology) (?:problem|question|homework)",
            r"step by step|show me how|walk me through",
            r"what does .+ mean",
            r"how does .+ work",
            r"why is .+ important",
            r"compare .+ and .+",
            r"difference between .+ and .+",
            r"examples? of .+",
            r"practice problems?",
            r"study guide|review|preparation",
        ]
    
    def is_educational_content(self, content: str) -> bool:
        """
        Determine if content is educational and appropriate.
        
        Args:
            content: The content to analyze
            
        Returns:
            bool: True if content is educational, False otherwise
        """
        try:
            content_lower = content.lower()
            
            # Check for non-educational keywords (immediate rejection)
            if self._contains_non_educational_keywords(content_lower):
                logger.info("Content rejected: contains non-educational keywords")
                return False
            
            # Check for educational patterns
            if self._matches_educational_patterns(content_lower):
                logger.info("Content accepted: matches educational patterns")
                return True
            
            # Check for educational keywords
            if self._contains_educational_keywords(content_lower):
                logger.info("Content accepted: contains educational keywords")
                return True
            
            # Check for subject-specific keywords
            if self._contains_subject_keywords(content_lower):
                logger.info("Content accepted: contains subject-specific keywords")
                return True
            
            # If content is very short, be more lenient
            if len(content.strip()) < 50:
                # Short content might be a simple question
                question_words = ["what", "how", "why", "when", "where", "who", "which"]
                if any(word in content_lower for word in question_words):
                    logger.info("Content accepted: short question format")
                    return True
            
            # Default to rejection for unclear content
            logger.info("Content rejected: no clear educational indicators")
            return False
            
        except Exception as e:
            logger.error(f"Error in content filtering: {e}")
            # Default to allowing content if filtering fails
            return True
    
    def _contains_non_educational_keywords(self, content: str) -> bool:
        """Check if content contains non-educational keywords."""
        return any(keyword in content for keyword in self.non_educational_keywords)
    
    def _contains_educational_keywords(self, content: str) -> bool:
        """Check if content contains educational keywords."""
        # Count educational keywords
        keyword_count = sum(1 for keyword in self.educational_keywords if keyword in content)
        
        # Require at least 1 educational keyword for shorter content,
        # or 2+ for longer content
        threshold = 2 if len(content) > 200 else 1
        return keyword_count >= threshold
    
    def _contains_subject_keywords(self, content: str) -> bool:
        """Check if content contains subject-specific keywords."""
        for subject, keywords in self.subject_keywords.items():
            if any(keyword in content for keyword in keywords):
                return True
        return False
    
    def _matches_educational_patterns(self, content: str) -> bool:
        """Check if content matches educational question patterns."""
        for pattern in self.educational_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False
    
    def get_educational_context(self, content: str) -> dict:
        """
        Get educational context information from content.
        
        Args:
            content: The content to analyze
            
        Returns:
            dict: Educational context information
        """
        content_lower = content.lower()
        context = {
            "detected_subjects": [],
            "educational_indicators": [],
            "question_type": "general",
            "complexity_level": "medium"
        }
        
        # Detect subjects
        for subject, keywords in self.subject_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                context["detected_subjects"].append(subject.value)
        
        # Detect educational indicators
        for keyword in self.educational_keywords:
            if keyword in content_lower:
                context["educational_indicators"].append(keyword)
        
        # Determine question type
        if any(word in content_lower for word in ["solve", "calculate", "compute"]):
            context["question_type"] = "problem_solving"
        elif any(word in content_lower for word in ["explain", "describe", "what is"]):
            context["question_type"] = "conceptual"
        elif any(word in content_lower for word in ["compare", "contrast", "difference"]):
            context["question_type"] = "comparative"
        elif any(word in content_lower for word in ["analyze", "evaluate", "assess"]):
            context["question_type"] = "analytical"
        
        # Estimate complexity (simple heuristic)
        if len(content) > 300 or any(word in content_lower for word in ["complex", "advanced", "detailed"]):
            context["complexity_level"] = "high"
        elif len(content) < 100 or any(word in content_lower for word in ["simple", "basic", "easy"]):
            context["complexity_level"] = "low"
        
        return context
