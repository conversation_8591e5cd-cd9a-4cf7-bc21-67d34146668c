#!/usr/bin/env python3
"""
Download Gemma 3N E2B model for local use.
"""

import os
from transformers import Gemma3nForConditionalGeneration, AutoProcessor
from huggingface_hub import login
from loguru import logger

def download_e2b_model():
    """Download the Gemma 3N E2B model and processor."""
    model_name = "google/gemma-3n-e2b-it"
    cache_dir = "./models"
    
    # Create cache directory
    os.makedirs(cache_dir, exist_ok=True)
    
    # Get HF token from environment
    hf_token = os.getenv("HF_TOKEN", "*************************************")
    
    if hf_token:
        try:
            login(token=hf_token)
            logger.info("Logged in to Hugging Face")
        except Exception as e:
            logger.warning(f"Could not login to HF: {e}")
    
    try:
        logger.info(f"Downloading {model_name} to {cache_dir}")
        
        # Download model
        logger.info("Downloading model...")
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=True,
            token=hf_token
        )
        logger.info("Model downloaded successfully")
        
        # Download processor
        logger.info("Downloading processor...")
        processor = AutoProcessor.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=True,
            token=hf_token
        )
        logger.info("Processor downloaded successfully")
        
        logger.info(f"Gemma 3N E2B model and processor downloaded to {cache_dir}")
        
    except Exception as e:
        logger.error(f"Failed to download model: {e}")
        raise

if __name__ == "__main__":
    download_e2b_model()
