#!/usr/bin/env python3
"""
Test script to safely load Gemma 3N E2B model without crashing laptop.
"""

import torch
import gc
import os
from transformers import Gemma3nForConditionalGeneration, AutoProcessor, BitsAndBytesConfig
from loguru import logger

def clear_memory():
    """Clear all possible memory."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    gc.collect()

def check_gpu_memory():
    """Check available GPU memory."""
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        allocated_memory = torch.cuda.memory_allocated() / 1024**3
        free_memory = total_memory - allocated_memory
        logger.info(f"GPU Memory - Total: {total_memory:.2f}GB, Used: {allocated_memory:.2f}GB, Free: {free_memory:.2f}GB")
        return free_memory
    return 0

def test_model_loading():
    """Test loading the model with crash prevention."""
    model_name = "google/gemma-3n-e2b-it"
    
    logger.info("Starting safe model loading test...")
    
    # Clear memory first
    clear_memory()
    free_memory = check_gpu_memory()
    
    # Determine loading strategy based on available memory
    if free_memory < 4.0:
        logger.warning(f"Only {free_memory:.2f}GB GPU memory available - using CPU mode")
        device_map = "cpu"
        torch_dtype = torch.float32
        quantization_config = None
        max_memory = None
        offload_buffers = False
    else:
        logger.info(f"{free_memory:.2f}GB GPU memory available - trying conservative GPU mode without quantization")
        device_map = "auto"
        torch_dtype = torch.float16
        quantization_config = None  # Disable quantization for now
        max_memory = {0: "3GB", "cpu": "8GB"}
        offload_buffers = True
    
    try:
        logger.info("Loading model...")
        model = Gemma3nForConditionalGeneration.from_pretrained(
            model_name,
            device_map=device_map,
            torch_dtype=torch_dtype,
            quantization_config=quantization_config,
            low_cpu_mem_usage=True,
            trust_remote_code=True,
            max_memory=max_memory,
            offload_buffers=offload_buffers,
            cache_dir="./models"
        ).eval()
        
        logger.info("Model loaded successfully!")
        check_gpu_memory()
        
        # Test basic functionality
        logger.info("Testing basic model functionality...")
        
        # Load processor
        processor = AutoProcessor.from_pretrained(
            model_name,
            cache_dir="./models",
            trust_remote_code=True
        )
        
        # Simple test
        messages = [
            {"role": "user", "content": [{"type": "text", "text": "Hello, how are you?"}]}
        ]
        
        inputs = processor.apply_chat_template(
            messages,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt",
        )
        
        if device_map != "cpu":
            inputs = inputs.to(model.device)
        
        with torch.inference_mode():
            generation = model.generate(**inputs, max_new_tokens=50, do_sample=False)
        
        response = processor.decode(generation[0][inputs["input_ids"].shape[-1]:], skip_special_tokens=True)
        logger.info(f"Model response: {response}")
        
        logger.info("✅ Model test completed successfully!")
        return True
        
    except torch.cuda.OutOfMemoryError as e:
        logger.error(f"❌ GPU out of memory: {e}")
        logger.info("Try running with DEVICE=cpu in .env file")
        return False
        
    except Exception as e:
        logger.error(f"❌ Model loading failed: {e}")
        return False
    
    finally:
        # Clean up
        clear_memory()

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("\n✅ Model can be loaded safely!")
    else:
        print("\n❌ Model loading failed - check logs above")
