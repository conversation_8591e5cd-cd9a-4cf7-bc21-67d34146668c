[project]
name = "gemma-3n"
version = "0.1.0"
description = "Educational Chat API with Gemma 3 - Multimodal AI Assistant for Learning"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "transformers>=4.53.2",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "torchaudio>=2.0.0",
    "pillow>=10.0.0",
    "opencv-python>=4.8.0",
    "numpy>=1.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "redis>=5.0.0",
    "speechrecognition>=3.10.0",
    "pydub>=0.25.0",
    "pytesseract>=0.3.10",
    "easyocr>=1.7.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "httpx>=0.25.0",
    "accelerate>=1.9.0",
    "huggingface-hub[cli]>=0.33.4",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/gemma_3n"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
